#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试面积过滤调整效果
验证远距离小矩形是否能通过调整后的过滤
"""

import cv2
import numpy as np

# 模拟调整后的面积过滤逻辑
MIN_CONTOUR_AREA = 400       # 原始最小轮廓面积
MAX_CONTOUR_AREA = 60000     # 最大轮廓面积

def test_area_filtering():
    """测试面积过滤调整"""
    
    print("🧪 面积过滤调整测试")
    print("=" * 40)
    print(f"原始最小面积: {MIN_CONTOUR_AREA} 像素")
    print(f"最大面积: {MAX_CONTOUR_AREA} 像素")
    print()
    
    # 测试不同大小的矩形
    test_areas = [
        (50, "极小矩形 (远距离)"),
        (100, "小矩形 (远距离)"),
        (200, "中小矩形"),
        (400, "原始最小矩形"),
        (1000, "正常矩形"),
        (5000, "大矩形"),
        (70000, "超大矩形")
    ]
    
    for area, description in test_areas:
        # 原始过滤逻辑
        original_pass = MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA
        
        # 调整后的过滤逻辑
        min_area_for_detection = min(MIN_CONTOUR_AREA, 100)
        adjusted_pass = min_area_for_detection < area < MAX_CONTOUR_AREA
        
        status_original = "✅通过" if original_pass else "🚫过滤"
        status_adjusted = "✅通过" if adjusted_pass else "🚫过滤"
        
        change_indicator = ""
        if original_pass != adjusted_pass:
            if adjusted_pass:
                change_indicator = " 🎯新增通过"
            else:
                change_indicator = " ⚠️新增过滤"
        
        print(f"{description:15} ({area:5}px): 原始={status_original} | 调整后={status_adjusted}{change_indicator}")
    
    print()
    print("📊 调整效果总结:")
    print("- 100像素以下的小矩形现在可以通过初步面积过滤")
    print("- 原来能检测的矩形仍然能检测")
    print("- 超大矩形仍然被过滤")
    print("- 基础过滤会进一步筛选真正的噪点")

if __name__ == "__main__":
    test_area_filtering()
