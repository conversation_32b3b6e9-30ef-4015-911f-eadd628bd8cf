#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础过滤功能测试脚本
测试面积占比过滤和边界距离过滤是否正常工作
"""

import cv2
import numpy as np

# 导入主程序中的过滤函数和参数
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟主程序中的参数（已调整）
MAX_AREA_RATIO = 0.25        # 最大面积占比（防止整个屏幕被识别）
MIN_AREA_RATIO = 0.002       # 最小面积占比（防止噪点）- 降低以支持远距离检测
MIN_BOUNDARY_DISTANCE = 8    # 最小边界距离（像素）- 减少以支持边缘检测

def basic_noise_filter(img_shape, approx):
    """基础噪点过滤 - 面积占比和边界距离检查"""
    
    # 1. 面积占比检查
    contour_area = cv2.contourArea(approx)
    total_area = img_shape[0] * img_shape[1]
    area_ratio = contour_area / total_area
    
    if area_ratio > MAX_AREA_RATIO:
        return False, f"面积过大: {area_ratio:.2%} (可能是整个屏幕)"
    
    if area_ratio < MIN_AREA_RATIO:
        return False, f"面积过小: {area_ratio:.2%} (可能是噪点)"
    
    # 2. 边界距离检查
    h, w = img_shape[:2]
    near_boundary_count = 0
    
    for point in approx:
        x, y = point[0]
        if (x < MIN_BOUNDARY_DISTANCE or x > w - MIN_BOUNDARY_DISTANCE or 
            y < MIN_BOUNDARY_DISTANCE or y > h - MIN_BOUNDARY_DISTANCE):
            near_boundary_count += 1
    
    # 只有当所有4个顶点都在边界附近时，才认为是屏幕边缘
    if near_boundary_count >= 4:
        return False, f"所有顶点都接近边界: {near_boundary_count}/4 (可能是屏幕边缘)"
    
    return True, f"通过基础过滤 (面积占比: {area_ratio:.2%}, 边界顶点: {near_boundary_count}/4)"

def test_basic_filter():
    """测试基础过滤功能"""
    
    # 创建测试图像 (320x240)
    img_shape = (240, 320, 3)
    h, w = img_shape[:2]
    
    print("🧪 基础过滤功能测试")
    print("=" * 50)
    print(f"测试图像尺寸: {w}x{h}")
    print(f"总面积: {w * h} 像素")
    print(f"面积占比范围: {MIN_AREA_RATIO:.1%} - {MAX_AREA_RATIO:.0%}")
    print(f"边界距离: {MIN_BOUNDARY_DISTANCE} 像素")
    print("⚠️  调整后的参数：更宽松的过滤条件")
    print()
    
    # 测试用例1: 正常矩形 (应该通过)
    normal_rect = np.array([
        [[50, 50]],
        [[150, 50]],
        [[150, 100]],
        [[50, 100]]
    ], dtype=np.int32)
    
    passed, reason = basic_noise_filter(img_shape, normal_rect)
    print(f"✅ 测试1 - 正常矩形: {'通过' if passed else '失败'}")
    print(f"   结果: {reason}")
    print()
    
    # 测试用例2: 整个屏幕 (应该被过滤)
    full_screen = np.array([
        [[0, 0]],
        [[w-1, 0]],
        [[w-1, h-1]],
        [[0, h-1]]
    ], dtype=np.int32)
    
    passed, reason = basic_noise_filter(img_shape, full_screen)
    print(f"🚫 测试2 - 整个屏幕: {'通过' if passed else '被过滤'}")
    print(f"   结果: {reason}")
    print()
    
    # 测试用例3: 小噪点 (应该被过滤)
    small_noise = np.array([
        [[100, 100]],
        [[102, 100]],
        [[102, 102]],
        [[100, 102]]
    ], dtype=np.int32)
    
    passed, reason = basic_noise_filter(img_shape, small_noise)
    print(f"🚫 测试3 - 小噪点: {'通过' if passed else '被过滤'}")
    print(f"   结果: {reason}")
    print()
    
    # 测试用例4: 屏幕边缘矩形 (应该被过滤)
    edge_rect = np.array([
        [[5, 5]],
        [[100, 5]],
        [[100, 50]],
        [[5, 50]]
    ], dtype=np.int32)
    
    passed, reason = basic_noise_filter(img_shape, edge_rect)
    print(f"🚫 测试4 - 屏幕边缘: {'通过' if passed else '被过滤'}")
    print(f"   结果: {reason}")
    print()
    
    # 测试用例5: 边界临界矩形 (应该通过)
    boundary_rect = np.array([
        [[20, 20]],
        [[120, 20]],
        [[120, 70]],
        [[20, 70]]
    ], dtype=np.int32)

    passed, reason = basic_noise_filter(img_shape, boundary_rect)
    print(f"✅ 测试5 - 边界临界: {'通过' if passed else '失败'}")
    print(f"   结果: {reason}")
    print()

    # 测试用例6: 远距离小矩形 (应该通过)
    small_distant_rect = np.array([
        [[200, 150]],
        [[210, 150]],
        [[210, 160]],
        [[200, 160]]
    ], dtype=np.int32)

    passed, reason = basic_noise_filter(img_shape, small_distant_rect)
    print(f"✅ 测试6 - 远距离小矩形: {'通过' if passed else '失败'}")
    print(f"   结果: {reason}")
    print()

    # 测试用例7: 3个顶点在边界的矩形 (现在应该通过)
    three_edge_rect = np.array([
        [[5, 5]],
        [[50, 5]],
        [[50, 50]],
        [[5, 50]]
    ], dtype=np.int32)

    passed, reason = basic_noise_filter(img_shape, three_edge_rect)
    print(f"✅ 测试7 - 3个顶点在边界: {'通过' if passed else '失败'}")
    print(f"   结果: {reason}")
    print()
    
    print("🎯 测试总结:")
    print("- ✅ 正常矩形、边界临界矩形、远距离小矩形、3个顶点在边界的矩形应该通过")
    print("- 🚫 整个屏幕、小噪点应该被过滤")
    print("- ⚠️  调整后的参数更宽松，支持远距离和边缘检测")
    print("- 📊 面积占比: 0.2%-25%, 边界距离: 8px, 边界过滤: 4个顶点都在边界才过滤")

if __name__ == "__main__":
    test_basic_filter()
