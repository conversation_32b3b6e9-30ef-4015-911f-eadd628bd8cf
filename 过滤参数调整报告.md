# 过滤参数调整报告

## 🚨 问题反馈
用户反馈基础过滤功能虽然解决了整块屏幕和分割线的误检问题，但导致了新问题：
- ❌ 别的矩形识别不到了
- ❌ 远距离识别不行
- ❌ 近距离才能识别好一点
- ❌ 测角度更不用说了

## 🔍 问题分析

### 根本原因
通过Sequential thinking分析发现问题出在：

1. **MIN_AREA_RATIO过大**：0.5%对远距离小矩形太严格
2. **MIN_BOUNDARY_DISTANCE过大**：15像素对320×240图像太严格
3. **边界过滤逻辑过严**：3个顶点在边界就过滤，过于严格
4. **原始面积过滤冲突**：MIN_CONTOUR_AREA=400像素，远距离小矩形在第一步就被过滤

### 具体数值分析
- 图像尺寸：320×240 = 76,800像素
- 原MIN_AREA_RATIO=0.5% = 384像素
- 远距离10×10矩形 = 100像素 < 384像素 → 被过滤
- 边界15像素意味着有效区域变成290×210

## ⚙️ 调整方案

### 1. 降低面积占比阈值
```python
# 调整前
MIN_AREA_RATIO = 0.005  # 0.5%

# 调整后  
MIN_AREA_RATIO = 0.001  # 0.1%
```

### 2. 减少边界距离
```python
# 调整前
MIN_BOUNDARY_DISTANCE = 15  # 15像素

# 调整后
MIN_BOUNDARY_DISTANCE = 8   # 8像素
```

### 3. 放宽边界过滤逻辑
```python
# 调整前：3个或更多顶点在边界就过滤
if near_boundary_count >= 3:
    return False

# 调整后：只有4个顶点都在边界才过滤
if near_boundary_count >= 4:
    return False
```

### 4. 降低初始面积过滤阈值
```python
# 调整前
if not (MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA):  # 400 < area < 60000
    continue

# 调整后
min_area_for_detection = 80  # 允许更小的矩形通过初步筛选
if not (min_area_for_detection < area < MAX_CONTOUR_AREA):  # 80 < area < 60000
    continue
```

## 📊 调整效果对比

| 参数 | 调整前 | 调整后 | 效果 |
|------|--------|--------|------|
| 最小面积占比 | 0.5% (384px) | 0.1% (77px) | 支持更小矩形 |
| 边界距离 | 15px | 8px | 减少边缘误杀 |
| 边界过滤 | ≥3个顶点 | ≥4个顶点 | 只过滤真正的屏幕边缘 |
| 初始面积过滤 | >400px | >80px | 远距离小矩形可通过 |

## ✅ 预期改善

### 远距离检测
- ✅ 80-400像素的矩形现在可以通过初始过滤
- ✅ 面积占比0.1%-0.5%的矩形可以通过基础过滤
- ✅ 支持更远距离的小目标检测

### 边缘检测
- ✅ 靠近边缘但有效的矩形不会被误杀
- ✅ 只有真正的屏幕边缘（4个顶点都在边界）才被过滤
- ✅ 边界距离从15px减少到8px，增加有效检测区域

### 角度检测
- ✅ 更多矩形能通过初步筛选，进入角度检测环节
- ✅ 减少因面积或边界过滤导致的角度检测失败

## 🛡️ 保持的保护功能

### 仍然过滤的目标
- 🚫 整个屏幕（面积占比>25%）
- 🚫 真正的小噪点（<80像素且<0.1%面积占比）
- 🚫 真正的屏幕边缘（4个顶点都在8px边界内）

### 双重保护机制
1. **初始面积过滤**：80px < area < 60000px
2. **基础过滤**：面积占比0.1%-25% + 边界检查

## 🎯 使用建议

### 如果仍有问题
1. **远距离仍检测不到**：可进一步降低min_area_for_detection到50px
2. **边缘仍有误杀**：可进一步降低MIN_BOUNDARY_DISTANCE到5px
3. **小目标被误杀**：可降低MIN_AREA_RATIO到0.0005 (0.05%)

### 监控方法
- 观察屏幕显示：`Filter: Area 0.1%-25%, Boundary 8px, MinArea 80px`
- 查看控制台输出的过滤原因
- 根据实际效果微调参数

## 📈 总结

通过4个关键参数的调整，在保持对整块屏幕和分割线误检防护的同时，显著改善了：
- ✅ 远距离小矩形检测能力
- ✅ 边缘区域检测能力  
- ✅ 角度检测的成功率
- ✅ 整体检测的灵敏度

调整后的过滤策略更加智能和宽松，应该能解决用户反馈的所有问题。
